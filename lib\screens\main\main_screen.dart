import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../core/theme/app_theme.dart';
import '../../services/audio_player_service.dart';
import '../home/<USER>';
import '../search/search_screen.dart';
import '../library/library_screen.dart';
import '../../widgets/mini_player.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const SearchScreen(),
    const LibraryScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: Column(
        children: [
          // Main content
          Expanded(child: _screens[_currentIndex]),

          // Mini player
          Consumer<AudioPlayerService>(
            builder: (context, audioService, child) {
              if (audioService.currentSong != null) {
                return const MiniPlayer();
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        color: AppTheme.spotifyDarkGrey,
        border: Border(
          top: BorderSide(color: AppTheme.spotifyLightGrey, width: 0.5),
        ),
      ),
      child: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        backgroundColor: Colors.transparent,
        elevation: 0,
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppTheme.spotifyGreen,
        unselectedItemColor: AppTheme.spotifyOffWhite,
        selectedFontSize: 12,
        unselectedFontSize: 12,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search_outlined),
            activeIcon: Icon(Icons.search),
            label: 'Search',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.library_music_outlined),
            activeIcon: Icon(Icons.library_music),
            label: 'Your Library',
          ),
        ],
      ),
    );
  }
}

// Temporary placeholder screens
class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        title: const Text('Good evening'),
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () {},
          ),
        ],
      ),
      body: const SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quick access tiles
            Text(
              'Recently played',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.spotifyWhite,
              ),
            ),
            SizedBox(height: 16),

            // Placeholder content
            Center(
              child: Column(
                children: [
                  SizedBox(height: 100),
                  Icon(
                    Icons.music_note,
                    size: 80,
                    color: AppTheme.spotifyGreen,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Welcome to MuseAI!',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.spotifyWhite,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Start by searching for music or creating a playlist',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.spotifyOffWhite,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SearchScreen extends StatelessWidget {
  const SearchScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        title: const Text('Search'),
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
      ),
      body: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            // Search bar placeholder
            TextField(
              decoration: InputDecoration(
                hintText: 'What do you want to listen to?',
                prefixIcon: Icon(Icons.search),
                filled: true,
                fillColor: AppTheme.spotifyGrey,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(8)),
                  borderSide: BorderSide.none,
                ),
              ),
            ),

            SizedBox(height: 32),

            // Placeholder content
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.search, size: 80, color: AppTheme.spotifyGreen),
                    SizedBox(height: 16),
                    Text(
                      'Search for music',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.spotifyWhite,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Find songs, artists, albums, and more',
                      style: TextStyle(
                        fontSize: 16,
                        color: AppTheme.spotifyOffWhite,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class LibraryScreen extends StatelessWidget {
  const LibraryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        title: const Text('Your Library'),
        backgroundColor: AppTheme.spotifyBlack,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.search), onPressed: () {}),
          IconButton(icon: const Icon(Icons.add), onPressed: () {}),
        ],
      ),
      body: const Padding(
        padding: EdgeInsets.all(16),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.library_music, size: 80, color: AppTheme.spotifyGreen),
              SizedBox(height: 16),
              Text(
                'Your Library',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.spotifyWhite,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'Your playlists and saved music will appear here',
                style: TextStyle(fontSize: 16, color: AppTheme.spotifyOffWhite),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
