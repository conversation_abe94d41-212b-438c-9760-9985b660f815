import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../services/ai_service.dart';
import '../../widgets/genre_chip.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  final SpeechToText _speechToText = SpeechToText();
  final AIService _aiService = AIService();
  
  bool _isListening = false;
  bool _isSearching = false;
  String _searchQuery = '';
  List<Map<String, dynamic>> _searchResults = [];

  @override
  void initState() {
    super.initState();
    _initializeSpeech();
  }

  Future<void> _initializeSpeech() async {
    await _speechToText.initialize();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      body: CustomScrollView(
        slivers: [
          // App Bar with Search
          SliverAppBar(
            backgroundColor: AppTheme.spotifyBlack,
            elevation: 0,
            floating: true,
            snap: true,
            title: Text(
              'Search',
              style: AppTheme.sectionHeader,
            ),
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(80),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: _buildSearchBar(),
              ),
            ),
          ),
          
          // Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _searchQuery.isEmpty
                  ? _buildBrowseContent()
                  : _buildSearchResults(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextField(
        controller: _searchController,
        style: const TextStyle(color: AppTheme.spotifyWhite),
        decoration: InputDecoration(
          hintText: 'What do you want to listen to?',
          hintStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
          prefixIcon: const Icon(
            Icons.search,
            color: AppTheme.spotifyOffWhite,
          ),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Voice search button
              IconButton(
                icon: Icon(
                  _isListening ? Icons.mic : Icons.mic_none,
                  color: _isListening ? AppTheme.spotifyGreen : AppTheme.spotifyOffWhite,
                ),
                onPressed: _toggleVoiceSearch,
              ),
              
              // AI magic button
              IconButton(
                icon: const Icon(
                  Icons.auto_awesome,
                  color: AppTheme.spotifyGreen,
                ),
                onPressed: _showAIPlaylistDialog,
              ),
            ],
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
          if (value.isNotEmpty) {
            _performSearch(value);
          }
        },
        onSubmitted: (value) {
          if (value.isNotEmpty) {
            _performSearch(value);
          }
        },
      ),
    );
  }

  Widget _buildBrowseContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // AI Features Section
        _buildAIFeaturesSection(),
        
        const SizedBox(height: 32),
        
        // Browse by Genre
        Text(
          'Browse all',
          style: AppTheme.sectionHeader,
        ),
        
        const SizedBox(height: 16),
        
        _buildGenreGrid(),
      ],
    );
  }

  Widget _buildAIFeaturesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'AI-Powered Features',
          style: AppTheme.sectionHeader,
        ),
        
        const SizedBox(height: 16),
        
        // AI Feature Cards
        Row(
          children: [
            Expanded(
              child: _buildAIFeatureCard(
                title: 'Create Playlist',
                subtitle: 'Describe what you want',
                icon: Icons.auto_awesome,
                onTap: _showAIPlaylistDialog,
              ),
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: _buildAIFeatureCard(
                title: 'Voice Search',
                subtitle: 'Speak your request',
                icon: Icons.mic,
                onTap: _toggleVoiceSearch,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildAIFeatureCard(
                title: 'Smart Discovery',
                subtitle: 'Find similar songs',
                icon: Icons.explore,
                onTap: () {
                  // TODO: Navigate to discovery
                },
              ),
            ),
            
            const SizedBox(width: 12),
            
            Expanded(
              child: _buildAIFeatureCard(
                title: 'Mood Playlists',
                subtitle: 'Music for your mood',
                icon: Icons.sentiment_satisfied,
                onTap: _showMoodDialog,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAIFeatureCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.spotifyGrey,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              icon,
              color: AppTheme.spotifyGreen,
              size: 32,
            ),
            
            const SizedBox(height: 12),
            
            Text(
              title,
              style: const TextStyle(
                color: AppTheme.spotifyWhite,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 4),
            
            Text(
              subtitle,
              style: const TextStyle(
                color: AppTheme.spotifyOffWhite,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenreGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.5,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: AppConstants.popularGenres.length,
      itemBuilder: (context, index) {
        final genre = AppConstants.popularGenres[index];
        return GenreChip(
          genre: genre,
          onTap: () {
            _searchController.text = genre;
            _performSearch(genre);
          },
        );
      },
    );
  }

  Widget _buildSearchResults() {
    if (_isSearching) {
      return const Center(
        child: CircularProgressIndicator(
          color: AppTheme.spotifyGreen,
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          children: [
            const SizedBox(height: 64),
            const Icon(
              Icons.search_off,
              size: 64,
              color: AppTheme.spotifyOffWhite,
            ),
            const SizedBox(height: 16),
            Text(
              'No results found',
              style: AppTheme.darkTheme.textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Try searching for something else',
              style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.spotifyOffWhite,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Search results for "$_searchQuery"',
          style: AppTheme.sectionHeader,
        ),
        
        const SizedBox(height: 16),
        
        // TODO: Display search results
        Text(
          'Found ${_searchResults.length} results',
          style: AppTheme.darkTheme.textTheme.bodyMedium?.copyWith(
            color: AppTheme.spotifyOffWhite,
          ),
        ),
      ],
    );
  }

  Future<void> _toggleVoiceSearch() async {
    if (_isListening) {
      await _speechToText.stop();
      setState(() {
        _isListening = false;
      });
    } else {
      final available = await _speechToText.initialize();
      if (available) {
        setState(() {
          _isListening = true;
        });
        
        await _speechToText.listen(
          onResult: (result) {
            setState(() {
              _searchController.text = result.recognizedWords;
              _searchQuery = result.recognizedWords;
            });
            
            if (result.finalResult) {
              _performSearch(result.recognizedWords);
              setState(() {
                _isListening = false;
              });
            }
          },
          listenFor: AppConstants.voiceListenTimeout,
          pauseFor: AppConstants.voicePauseTimeout,
        );
      }
    }
  }

  Future<void> _performSearch(String query) async {
    setState(() {
      _isSearching = true;
      _searchQuery = query;
    });

    try {
      // TODO: Implement actual search logic
      await Future.delayed(const Duration(seconds: 1)); // Simulate search
      
      setState(() {
        _searchResults = []; // Placeholder
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Search failed: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    }
  }

  void _showAIPlaylistDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Create AI Playlist',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: const Text(
          'Describe the playlist you want to create. For example: "upbeat songs for working out" or "sad songs from the 2000s"',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Show AI playlist creation screen
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _showMoodDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Select Mood',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        content: SizedBox(
          width: double.maxFinite,
          child: Wrap(
            spacing: 8,
            runSpacing: 8,
            children: AppConstants.moodCategories.map((mood) {
              return FilterChip(
                label: Text(mood),
                onSelected: (selected) {
                  if (selected) {
                    Navigator.pop(context);
                    _searchController.text = '$mood music';
                    _performSearch('$mood music');
                  }
                },
                backgroundColor: AppTheme.spotifyDarkGrey,
                selectedColor: AppTheme.spotifyGreen,
                labelStyle: const TextStyle(color: AppTheme.spotifyWhite),
              );
            }).toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    _speechToText.cancel();
    super.dispose();
  }
}
